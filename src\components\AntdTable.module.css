/* 自定义表格样式 */
.custom-table .ant-table {
  border-radius: 12px;
  overflow: hidden;
}

.custom-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  color: #374151;
  padding: 16px 12px;
}

.custom-table .ant-table-tbody > tr {
  transition: all 0.2s ease;
}

.custom-table .ant-table-tbody > tr:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.custom-table .ant-table-tbody > tr > td {
  padding: 12px;
  border-bottom: 1px solid #f1f5f9;
}

.custom-table .ant-table-row-selected {
  background-color: #eff6ff !important;
}

.custom-table .ant-table-row-selected:hover {
  background-color: #dbeafe !important;
}

/* 自定义分页样式 */
.custom-pagination .ant-pagination-item {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.custom-pagination .ant-pagination-item:hover {
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.custom-pagination .ant-pagination-item-active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #3b82f6;
}

.custom-pagination .ant-pagination-item-active a {
  color: white;
}

.custom-pagination .ant-pagination-prev,
.custom-pagination .ant-pagination-next {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.custom-pagination .ant-pagination-prev:hover,
.custom-pagination .ant-pagination-next:hover {
  border-color: #3b82f6;
  transform: translateY(-1px);
}

/* 自定义Modal样式 */
.custom-modal .ant-modal-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  border-radius: 8px 8px 0 0;
  padding: 20px 24px;
}

.custom-modal .ant-modal-body {
  padding: 24px;
}

.custom-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 自定义Drawer样式 */
.custom-drawer .ant-drawer-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  padding: 20px 24px;
}

.custom-drawer .ant-drawer-body {
  padding: 24px;
}

.custom-drawer .ant-drawer-footer {
  padding: 16px 24px;
  background-color: #f8fafc;
}

/* 表单项样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #374151;
}

.ant-input,
.ant-select-selector {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

.ant-input:hover,
.ant-select-selector:hover {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.ant-input:focus,
.ant-select-focused .ant-select-selector {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮样式优化 */
.ant-btn {
  transition: all 0.2s ease;
  font-weight: 500;
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

/* Tag样式优化 */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 12px;
  border: none;
}

.ant-card-body {
  padding: 0;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .custom-table .ant-table-thead > tr > th,
  .custom-table .ant-table-tbody > tr > td {
    padding: 8px 6px;
    font-size: 12px;
  }

  .custom-modal .ant-modal-body,
  .custom-drawer .ant-drawer-body {
    padding: 16px;
  }
}

/* 加载动画优化 */
.ant-spin-dot-item {
  background-color: #3b82f6;
}

/* 滚动条样式 */
.ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 层次感增强样式 */
.search-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.search-section .ant-card-body {
  padding: 12px;
}

.search-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.action-row {
  background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.table-section {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.table-section:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.pagination-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 批量操作区域增强 */
.batch-operations {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
}

.batch-operations:hover {
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
}

/* 新增任务按钮增强 */
.add-task-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
}

.add-task-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  transform: translateY(-2px);
}

/* 搜索表单标题样式 */
.search-title {
  color: #1f2937;
  font-weight: 600;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.search-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 2px;
  margin-right: 8px;
}

/* 数据统计样式 */
.data-stats {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-weight: 500;
}

.data-stats::before {
  content: '';
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 卡片阴影层次 */
.level-1-card {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.level-2-card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
}

.level-3-card {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* 响应式优化增强 */
@media (max-width: 768px) {
  .search-section {
    margin: 8px;
  }

  .action-row {
    padding: 12px;
    margin: 8px;
  }

  .batch-operations {
    padding: 8px 12px;
  }
}
